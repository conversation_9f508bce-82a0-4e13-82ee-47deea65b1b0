#!/usr/bin/env python3
"""
OCR后端服务启动脚本
"""

import os
import sys
from app import app
from config import get_config

def main():
    """主函数"""
    # 获取配置
    config_class = get_config()
    
    # 应用配置
    app.config.from_object(config_class)
    
    # 创建必要的目录
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    
    print("=" * 60)
    print("🚀 OCR Backend Service")
    print("=" * 60)
    print(f"📍 地址: http://{config_class.HOST}:{config_class.PORT}")
    print(f"🔧 环境: {'开发' if config_class.DEBUG else '生产'}")
    print(f"📁 上传目录: {app.config['UPLOAD_FOLDER']}")
    print(f"🔑 API密钥: {'已配置' if config_class.YUNWU_API_KEY else '未配置'}")
    print(f"🤖 默认模型: {config_class.DEFAULT_MODEL}")
    print("=" * 60)
    print("📋 支持的文件类型:")
    for file_type, extensions in config_class.ALLOWED_EXTENSIONS.items():
        print(f"  • {file_type}: {', '.join(extensions)}")
    print("=" * 60)
    print("🔗 API接口:")
    print(f"  • 健康检查: GET  /health")
    print(f"  • 文档处理: POST /api/process")
    print(f"  • 支持类型: GET  /api/supported-types")
    print("=" * 60)
    
    # 启动服务
    try:
        app.run(
            host=config_class.HOST,
            port=config_class.PORT,
            debug=config_class.DEBUG
        )
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
