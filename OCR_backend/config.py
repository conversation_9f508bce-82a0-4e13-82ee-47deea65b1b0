"""
OCR后端服务配置文件
"""

import os
from pathlib import Path

class Config:
    """基础配置"""
    
    # 服务配置
    HOST = '0.0.0.0'
    PORT = 5050
    DEBUG = True
    
    # 文件上传配置
    MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 100MB
    UPLOAD_FOLDER = os.path.join(os.path.dirname(__file__), 'temp')
    
    # API配置
    YUNWU_API_KEY = os.environ.get('YUNWU_API_KEY')
    DEFAULT_MODEL = 'gemini-2.0-flash-exp'
    
    # 支持的文件类型
    ALLOWED_EXTENSIONS = {
        'pdf': ['.pdf'],
        'excel': ['.xlsx', '.xls', '.csv'],
        'html': ['.html', '.htm'],
        'image': ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff', '.tif'],
        'markdown': ['.md', '.markdown'],
        'word': ['.doc', '.docx']
    }
    
    # 处理超时时间（秒）
    PROCESSING_TIMEOUT = 300  # 5分钟
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    LOG_LEVEL = 'WARNING'

# 根据环境变量选择配置
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

def get_config():
    """获取当前配置"""
    env = os.environ.get('FLASK_ENV', 'default')
    return config.get(env, config['default'])
