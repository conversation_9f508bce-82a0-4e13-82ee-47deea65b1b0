#!/usr/bin/env python3
"""
独立的OCR处理后端服务
端口: 5050
功能: 接收各类文件，返回OCR处理后的Markdown内容
"""

import os
import sys
import uuid
import tempfile
import shutil
from pathlib import Path
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
from werkzeug.utils import secure_filename
import logging

# 添加OCR模块路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
ocr_path = os.path.join(parent_dir, 'OCR')
sys.path.insert(0, ocr_path)

# 导入OCR处理器
try:
    from document_utils import (
        PdfProcessor, ExcelProcessor, HtmlProcessor, 
        ImageProcessor, DocumentProcessor
    )
    OCR_AVAILABLE = True
except ImportError as e:
    print(f"警告: OCR模块导入失败: {e}")
    OCR_AVAILABLE = False

app = Flask(__name__)
CORS(app)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 配置
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB
app.config['UPLOAD_FOLDER'] = tempfile.gettempdir()

# 支持的文件类型
ALLOWED_EXTENSIONS = {
    'pdf': ['.pdf'],
    'excel': ['.xlsx', '.xls', '.csv'],
    'html': ['.html', '.htm'],
    'image': ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff', '.tif'],
    'markdown': ['.md', '.markdown'],
    'word': ['.doc', '.docx']
}

def allowed_file(filename):
    """检查文件类型是否支持"""
    if '.' not in filename:
        return False
    
    ext = '.' + filename.rsplit('.', 1)[1].lower()
    for file_type, extensions in ALLOWED_EXTENSIONS.items():
        if ext in extensions:
            return True
    return False

def get_file_type(filename):
    """根据文件名获取文件类型"""
    if '.' not in filename:
        return None
    
    ext = '.' + filename.rsplit('.', 1)[1].lower()
    for file_type, extensions in ALLOWED_EXTENSIONS.items():
        if ext in extensions:
            return file_type
    return None

def get_processor_class(file_type):
    """根据文件类型获取处理器类"""
    processor_map = {
        'pdf': PdfProcessor,
        'excel': ExcelProcessor,
        'html': HtmlProcessor,
        'image': ImageProcessor,
        'markdown': None,  # Markdown文件直接返回
        'word': None  # Word文件需要特殊处理
    }
    return processor_map.get(file_type)

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'service': 'OCR Backend',
        'port': 5050,
        'ocr_available': OCR_AVAILABLE
    })

@app.route('/api/process', methods=['POST'])
def process_document():
    """
    处理文档的主接口
    
    请求参数:
    - file: 上传的文件
    - api_key: (可选) 云雾AI API密钥，用于图像和扫描文档处理
    - model: (可选) 使用的AI模型，默认为 gemini-2.0-flash-exp
    
    返回:
    - success: 是否成功
    - content: 处理后的Markdown内容
    - filename: 原始文件名
    - file_type: 文件类型
    - error: 错误信息（如果失败）
    """
    try:
        # 检查OCR模块是否可用
        if not OCR_AVAILABLE:
            return jsonify({
                'success': False,
                'error': 'OCR模块不可用，请检查依赖安装'
            }), 500

        # 检查文件
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': '没有文件被上传'
            }), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': '没有选择文件'
            }), 400

        if not allowed_file(file.filename):
            return jsonify({
                'success': False,
                'error': f'不支持的文件类型。支持的类型: {", ".join([ext for exts in ALLOWED_EXTENSIONS.values() for ext in exts])}'
            }), 400

        # 获取参数
        api_key = request.form.get('api_key') or os.environ.get('YUNWU_API_KEY')
        model = request.form.get('model', 'gemini-2.0-flash-exp')

        # 确定文件类型
        file_type = get_file_type(file.filename)
        if not file_type:
            return jsonify({
                'success': False,
                'error': '无法确定文件类型'
            }), 400

        logger.info(f"开始处理文件: {file.filename}, 类型: {file_type}")

        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix='ocr_backend_')
        input_dir = os.path.join(temp_dir, 'input')
        output_dir = os.path.join(temp_dir, 'output')
        os.makedirs(input_dir, exist_ok=True)
        os.makedirs(output_dir, exist_ok=True)

        try:
            # 保存上传的文件
            filename = secure_filename(file.filename)
            input_file_path = os.path.join(input_dir, filename)
            file.save(input_file_path)

            # 处理文件
            result = process_file_by_type(
                input_file_path, output_dir, file_type, 
                api_key, model, filename
            )

            if result['success']:
                return jsonify({
                    'success': True,
                    'content': result['content'],
                    'filename': filename,
                    'file_type': file_type,
                    'processing_time': result.get('processing_time', 0)
                })
            else:
                return jsonify({
                    'success': False,
                    'error': result['error'],
                    'filename': filename,
                    'file_type': file_type
                }), 500

        finally:
            # 清理临时文件
            try:
                shutil.rmtree(temp_dir)
            except Exception as e:
                logger.warning(f"清理临时文件失败: {e}")

    except Exception as e:
        logger.error(f"处理请求失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': f'服务器内部错误: {str(e)}'
        }), 500

def process_file_by_type(input_file_path, output_dir, file_type, api_key, model, original_filename):
    """根据文件类型处理文件"""
    import time
    start_time = time.time()
    
    try:
        if file_type == 'markdown':
            # Markdown文件直接读取内容
            with open(input_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return {
                'success': True,
                'content': content,
                'processing_time': time.time() - start_time
            }
        
        elif file_type == 'word':
            # Word文件需要特殊处理
            return process_word_file(input_file_path, original_filename)
        
        else:
            # 其他文件类型使用对应的处理器
            processor_class = get_processor_class(file_type)
            if not processor_class:
                return {
                    'success': False,
                    'error': f'不支持的文件类型: {file_type}'
                }

            # 创建处理器实例
            if file_type == 'image':
                processor = processor_class(
                    input_path=os.path.dirname(input_file_path),
                    output_path=output_dir,
                    api_key=api_key,
                    model=model
                )
            elif file_type == 'pdf':
                processor = processor_class(
                    input_path=os.path.dirname(input_file_path),
                    output_path=output_dir,
                    save_intermediate=False,
                    api_key=api_key
                )
            else:
                processor = processor_class(
                    input_path=os.path.dirname(input_file_path),
                    output_path=output_dir
                )

            # 处理文件
            success = processor.process_file(Path(input_file_path))
            
            if success:
                # 查找生成的Markdown文件
                md_files = list(Path(output_dir).glob('*.md'))
                if md_files:
                    with open(md_files[0], 'r', encoding='utf-8') as f:
                        content = f.read()
                    return {
                        'success': True,
                        'content': content,
                        'processing_time': time.time() - start_time
                    }
                else:
                    return {
                        'success': False,
                        'error': '处理完成但未找到输出文件'
                    }
            else:
                return {
                    'success': False,
                    'error': '文件处理失败'
                }

    except Exception as e:
        logger.error(f"处理文件失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return {
            'success': False,
            'error': f'处理失败: {str(e)}'
        }

def process_word_file(input_file_path, original_filename):
    """处理Word文件"""
    try:
        # 这里可以添加Word文件处理逻辑
        # 目前返回一个提示信息
        return {
            'success': False,
            'error': 'Word文件处理功能暂未实现，请转换为PDF格式后上传'
        }
    except Exception as e:
        return {
            'success': False,
            'error': f'Word文件处理失败: {str(e)}'
        }

@app.route('/api/supported-types', methods=['GET'])
def get_supported_types():
    """获取支持的文件类型"""
    return jsonify({
        'success': True,
        'supported_types': ALLOWED_EXTENSIONS,
        'ocr_available': OCR_AVAILABLE
    })

if __name__ == '__main__':
    print("=" * 50)
    print("OCR Backend Service")
    print("=" * 50)
    print(f"端口: 5050")
    print(f"OCR模块可用: {OCR_AVAILABLE}")
    print(f"支持的文件类型: {list(ALLOWED_EXTENSIONS.keys())}")
    print("=" * 50)
    
    app.run(host='0.0.0.0', port=5050, debug=True)
